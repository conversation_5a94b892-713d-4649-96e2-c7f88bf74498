# 問題回饋功能文檔

## 功能概述

問題回饋功能允許用戶向開發團隊提交錯誤報告、功能建議或其他問題。該功能支援多種回饋類型、附件上傳和操作日誌附加，幫助開發團隊更好地了解和解決用戶遇到的問題。

## 功能特點

### 1. 回饋類型分類
- **錯誤回報**：用於報告應用程式中的錯誤或異常行為
- **功能建議**：用於提出新功能或改進現有功能的建議
- **其他問題**：用於其他類型的問題或意見

### 2. 內容填寫
- **問題描述**（必填）：詳細描述遇到的問題或建議
- **聯絡方式**（選填）：留下電子郵件地址以便開發團隊回覆

### 3. 附件支援
- **拍照**：直接使用相機拍攝截圖
- **相簿選擇**：從設備相簿中選擇多張圖片
- **附件管理**：可以預覽和刪除已選擇的附件

### 4. 日誌附加
- **操作日誌**：可選擇附上應用程式的操作日誌
- **自動收集**：包含設備資訊、應用版本等技術資料

## 使用流程

### 1. 進入回饋頁面
1. 開啟應用程式
2. 進入「設定」頁面
3. 點擊「問題回饋」選項

### 2. 填寫回饋內容
1. **選擇回饋類型**：根據問題性質選擇適當的類型
2. **填寫問題描述**：
   - 詳細描述遇到的問題
   - 說明重現步驟（如果是錯誤報告）
   - 提供期望的結果
3. **填寫聯絡方式**（可選）：
   - 如需開發團隊回覆，請留下有效的電子郵件地址

### 3. 添加附件（可選）
1. **拍照**：點擊「拍照」按鈕直接拍攝
2. **選擇圖片**：點擊「相簿」按鈕從相簿選擇
3. **管理附件**：可以刪除不需要的附件

### 4. 附加日誌（可選）
- 開啟「附上操作日誌」開關
- 系統會自動收集相關技術資料

### 5. 提交回饋
1. 檢查填寫的內容
2. 點擊「提交回饋」按鈕
3. 等待提交完成
4. 查看成功提示

## 技術實現

### 資料模型
```dart
class FeedbackModel {
  final String id;                    // 回饋ID
  final String content;               // 問題描述
  final String? email;                // 聯絡郵件
  final String? userId;               // 用戶ID
  final String deviceId;              // 設備ID
  final DateTime createdAt;           // 創建時間
  final FeedbackType type;            // 回饋類型
  final bool isResolved;              // 是否已解決
  final List<String> attachmentUrls;  // 附件URL列表
  final String? logFileUrl;           // 日誌文件URL
  final Map<String, dynamic>? deviceInfo; // 設備資訊
  final String? appVersion;           // 應用版本
}
```

### 回饋類型
```dart
enum FeedbackType {
  bugReport('錯誤回報'),
  featureRequest('功能建議'),
  other('其他問題');
}
```

### 服務架構
- **FeedbackService**：處理回饋的提交、查詢和管理
- **LogManagementService**：處理日誌文件的導出
- **Firebase Storage**：存儲附件和日誌文件
- **Firestore**：存儲回饋資料

## 後台管理

### 資料儲存
- 回饋資料存儲在 Firestore 的 `user_feedback` 集合中
- 附件文件存儲在 Firebase Storage 的 `feedback_attachments` 目錄
- 日誌文件存儲在 Firebase Storage 的 `feedback_logs` 目錄

### 管理功能
- 查看所有回饋
- 按類型和狀態篩選
- 標記為已解決
- 刪除回饋（包含相關文件）

## 隱私與安全

### 資料收集
- 僅收集用戶主動提供的資訊
- 設備資訊僅用於技術支援
- 日誌文件僅在用戶同意時收集

### 資料保護
- 所有資料加密傳輸和存儲
- 遵循相關隱私法規
- 用戶可要求刪除其回饋資料

## 最佳實踐

### 用戶指南
1. **詳細描述問題**：提供足夠的細節幫助開發團隊理解問題
2. **提供重現步驟**：對於錯誤報告，說明如何重現問題
3. **附加截圖**：視覺資料有助於快速定位問題
4. **留下聯絡方式**：如需回覆，請提供有效的聯絡方式

### 開發團隊指南
1. **及時回應**：盡快處理用戶回饋
2. **分類管理**：按優先級和類型處理回饋
3. **狀態更新**：及時更新處理狀態
4. **用戶溝通**：必要時與用戶進一步溝通

## 故障排除

### 常見問題
1. **提交失敗**：檢查網路連接和權限設定
2. **附件上傳失敗**：檢查文件大小和格式
3. **日誌收集失敗**：檢查應用權限

### 錯誤處理
- 網路錯誤：自動重試機制
- 權限錯誤：引導用戶授權
- 文件錯誤：提示用戶重新選擇

## 未來改進

### 計劃功能
1. **回饋狀態追蹤**：用戶可查看回饋處理進度
2. **自動分類**：使用AI自動分類回饋類型
3. **批量處理**：管理員批量處理回饋
4. **統計分析**：回饋趨勢和統計報告

### 技術優化
1. **離線支援**：支援離線提交回饋
2. **壓縮優化**：附件自動壓縮
3. **增量同步**：優化大文件上傳
4. **快取機制**：提升載入速度
