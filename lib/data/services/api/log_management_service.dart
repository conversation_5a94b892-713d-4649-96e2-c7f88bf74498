import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/utils/logger_utils.dart';
import '../../../core/utils/persistent_logger.dart';

/// 日誌管理服務
/// 提供日誌收集、分析、上傳和管理功能
class LogManagementService {
  static LogManagementService? _instance;
  static LogManagementService get instance => _instance ??= LogManagementService._();
  
  LogManagementService._();
  
  static const String _settingsKey = 'log_management_settings';
  static const String _crashReportsKey = 'crash_reports';
  static const String _performanceLogsKey = 'performance_logs';
  
  late SharedPreferences _prefs;
  bool _initialized = false;
  
  /// 日誌管理設定
  LogManagementSettings _settings = LogManagementSettings();
  
  /// 初始化日誌管理服務
  Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      
      // 初始化持久化日誌器
      await PersistentLogger.instance.initialize();
      
      _initialized = true;
      
      PersistentLogger.instance.i('LogManagementService 初始化成功');
      
      // 記錄應用啟動信息
      await _logAppStartInfo();
      
      // 如果啟用自動上傳，檢查是否需要上傳
      if (_settings.autoUpload) {
        _scheduleAutoUpload();
      }
      
    } catch (e) {
      print('LogManagementService 初始化失敗: $e');
    }
  }
  
  /// 載入設定
  Future<void> _loadSettings() async {
    try {
      final settingsJson = _prefs.getString(_settingsKey);
      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        _settings = LogManagementSettings.fromJson(settingsMap);
      }
    } catch (e) {
      print('載入日誌管理設定失敗: $e');
    }
  }
  
  /// 保存設定
  Future<void> _saveSettings() async {
    try {
      final settingsJson = jsonEncode(_settings.toJson());
      await _prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      PersistentLogger.instance.e('保存日誌管理設定失敗: $e');
    }
  }
  
  /// 記錄應用啟動信息
  Future<void> _logAppStartInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final packageInfo = await PackageInfo.fromPlatform();
      
      final startInfo = <String, dynamic>{
        'app_version': packageInfo.version,
        'build_number': packageInfo.buildNumber,
        'platform': kIsWeb ? 'web' : Platform.operatingSystem,
        'start_time': DateTime.now().toIso8601String(),
      };
      
      if (!kIsWeb) {
        if (Platform.isAndroid) {
          final androidInfo = await deviceInfo.androidInfo;
          startInfo['device_info'] = {
            'model': androidInfo.model,
            'manufacturer': androidInfo.manufacturer,
            'android_version': androidInfo.version.release,
            'sdk_int': androidInfo.version.sdkInt,
          };
        } else if (Platform.isIOS) {
          final iosInfo = await deviceInfo.iosInfo;
          startInfo['device_info'] = {
            'model': iosInfo.model,
            'name': iosInfo.name,
            'system_version': iosInfo.systemVersion,
          };
        }
      } else {
        final webInfo = await deviceInfo.webBrowserInfo;
        startInfo['device_info'] = {
          'browser_name': webInfo.browserName.name,
          'user_agent': webInfo.userAgent,
        };
      }
      
      PersistentLogger.instance.i('應用啟動信息: ${jsonEncode(startInfo)}');
      
    } catch (e) {
      PersistentLogger.instance.e('記錄應用啟動信息失敗: $e');
    }
  }
  
  /// 記錄崩潰報告
  Future<void> logCrashReport({
    required String error,
    required StackTrace stackTrace,
    Map<String, dynamic>? additionalInfo,
  }) async {
    try {
      final crashReport = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'timestamp': DateTime.now().toIso8601String(),
        'error': error.toString(),
        'stack_trace': stackTrace.toString(),
        'additional_info': additionalInfo ?? {},
      };
      
      // 保存到本地
      final crashReports = _prefs.getStringList(_crashReportsKey) ?? [];
      crashReports.add(jsonEncode(crashReport));
      
      // 只保留最近 20 個崩潰報告
      if (crashReports.length > 20) {
        crashReports.removeRange(0, crashReports.length - 20);
      }
      
      await _prefs.setStringList(_crashReportsKey, crashReports);
      
      // 記錄到日誌
      PersistentLogger.instance.e('崩潰報告: $error', error, stackTrace);
      
      // 如果啟用自動上傳，立即上傳
      if (_settings.autoUploadCrashes) {
        await uploadLogs();
      }
      
    } catch (e) {
      print('記錄崩潰報告失敗: $e');
    }
  }
  
  /// 記錄性能日誌
  Future<void> logPerformance({
    required String operation,
    required Duration duration,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      if (!_settings.enablePerformanceLogs) return;
      
      final performanceLog = {
        'timestamp': DateTime.now().toIso8601String(),
        'operation': operation,
        'duration_ms': duration.inMilliseconds,
        'metadata': metadata ?? {},
      };
      
      // 保存到本地
      final performanceLogs = _prefs.getStringList(_performanceLogsKey) ?? [];
      performanceLogs.add(jsonEncode(performanceLog));
      
      // 只保留最近 100 個性能日誌
      if (performanceLogs.length > 100) {
        performanceLogs.removeRange(0, performanceLogs.length - 100);
      }
      
      await _prefs.setStringList(_performanceLogsKey, performanceLogs);
      
      // 記錄到日誌
      PersistentLogger.instance.d('性能日誌: $operation 耗時 ${duration.inMilliseconds}ms');
      
    } catch (e) {
      PersistentLogger.instance.e('記錄性能日誌失敗: $e');
    }
  }
  
  /// 上傳日誌到 Firebase Storage
  Future<bool> uploadLogs() async {
    try {
      if (!_initialized) {
        logger.w('日誌管理服務未初始化');
        return false;
      }
      
      PersistentLogger.instance.i('開始上傳日誌到 Firebase Storage');
      
      // 上傳主日誌文件
      final logUploadSuccess = await PersistentLogger.instance.uploadCurrentLogFile();
      
      // 上傳崩潰報告
      final crashUploadSuccess = await _uploadCrashReports();
      
      // 上傳性能日誌
      final performanceUploadSuccess = await _uploadPerformanceLogs();
      
      // 更新最後上傳時間
      if (logUploadSuccess || crashUploadSuccess || performanceUploadSuccess) {
        _settings.lastUploadTime = DateTime.now();
        await _saveSettings();
      }
      
      final success = logUploadSuccess || crashUploadSuccess || performanceUploadSuccess;
      
      if (success) {
        PersistentLogger.instance.i('日誌上傳完成');
      } else {
        PersistentLogger.instance.w('日誌上傳失敗或無內容需要上傳');
      }
      
      return success;
      
    } catch (e) {
      PersistentLogger.instance.e('上傳日誌失敗: $e');
      return false;
    }
  }
  
  /// 上傳崩潰報告
  Future<bool> _uploadCrashReports() async {
    try {
      final crashReports = _prefs.getStringList(_crashReportsKey) ?? [];
      if (crashReports.isEmpty) return false;
      
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return false;
      
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'crash_reports_$timestamp.json';
      final storagePath = 'user_logs/${currentUser.uid}/crashes/$fileName';
      
      final crashData = {
        'upload_time': DateTime.now().toIso8601String(),
        'crash_reports': crashReports.map((report) => jsonDecode(report)).toList(),
      };
      
      final jsonData = jsonEncode(crashData);
      final storageRef = FirebaseStorage.instance.ref().child(storagePath);
      
      await storageRef.putString(jsonData, metadata: SettableMetadata(
        contentType: 'application/json',
        customMetadata: {
          'type': 'crash_reports',
          'count': crashReports.length.toString(),
        },
      ));
      
      // 清空本地崩潰報告
      await _prefs.remove(_crashReportsKey);
      
      PersistentLogger.instance.i('崩潰報告上傳成功: ${crashReports.length} 個報告');
      return true;
      
    } catch (e) {
      PersistentLogger.instance.e('上傳崩潰報告失敗: $e');
      return false;
    }
  }
  
  /// 上傳性能日誌
  Future<bool> _uploadPerformanceLogs() async {
    try {
      final performanceLogs = _prefs.getStringList(_performanceLogsKey) ?? [];
      if (performanceLogs.isEmpty) return false;
      
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return false;
      
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'performance_logs_$timestamp.json';
      final storagePath = 'user_logs/${currentUser.uid}/performance/$fileName';
      
      final performanceData = {
        'upload_time': DateTime.now().toIso8601String(),
        'performance_logs': performanceLogs.map((log) => jsonDecode(log)).toList(),
      };
      
      final jsonData = jsonEncode(performanceData);
      final storageRef = FirebaseStorage.instance.ref().child(storagePath);
      
      await storageRef.putString(jsonData, metadata: SettableMetadata(
        contentType: 'application/json',
        customMetadata: {
          'type': 'performance_logs',
          'count': performanceLogs.length.toString(),
        },
      ));
      
      // 清空本地性能日誌
      await _prefs.remove(_performanceLogsKey);
      
      PersistentLogger.instance.i('性能日誌上傳成功: ${performanceLogs.length} 個日誌');
      return true;
      
    } catch (e) {
      PersistentLogger.instance.e('上傳性能日誌失敗: $e');
      return false;
    }
  }
  
  /// 排程自動上傳
  void _scheduleAutoUpload() {
    // 這裡可以實作定時上傳邏輯
    // 例如每天上傳一次，或者當日誌文件達到一定大小時上傳
  }
  
  /// 獲取日誌統計信息
  Future<LogStatistics> getLogStatistics() async {
    try {
      final logFileSize = await PersistentLogger.instance.getLogFileSize();
      final uploadedLogs = PersistentLogger.instance.getUploadedLogs();
      final crashReports = _prefs.getStringList(_crashReportsKey) ?? [];
      final performanceLogs = _prefs.getStringList(_performanceLogsKey) ?? [];
      
      return LogStatistics(
        localLogFileSize: logFileSize,
        uploadedLogsCount: uploadedLogs.length,
        pendingCrashReports: crashReports.length,
        pendingPerformanceLogs: performanceLogs.length,
        lastUploadTime: _settings.lastUploadTime,
      );
      
    } catch (e) {
      PersistentLogger.instance.e('獲取日誌統計信息失敗: $e');
      return LogStatistics();
    }
  }
  
  /// 更新設定
  Future<void> updateSettings(LogManagementSettings settings) async {
    _settings = settings;
    await _saveSettings();
    PersistentLogger.instance.i('日誌管理設定已更新');
  }
  
  /// 獲取當前設定
  LogManagementSettings get settings => _settings;
  
  /// 清理所有本地日誌
  Future<void> clearAllLogs() async {
    try {
      await PersistentLogger.instance.clearLocalLogs();
      await _prefs.remove(_crashReportsKey);
      await _prefs.remove(_performanceLogsKey);

      PersistentLogger.instance.i('所有本地日誌已清理');
    } catch (e) {
      PersistentLogger.instance.e('清理本地日誌失敗: $e');
    }
  }

  /// 導出日誌到文件
  Future<File?> exportLogsToFile() async {
    try {
      if (!_initialized) {
        logger.w('日誌管理服務未初始化');
        return null;
      }

      // 先刷新緩衝區確保最新日誌被寫入
      await PersistentLogger.instance.flushBuffer();

      // 獲取當前日誌文件
      final logFile = PersistentLogger.instance.getCurrentLogFile();

      if (logFile == null || !await logFile.exists()) {
        logger.w('日誌文件不存在');
        return null;
      }

      // 創建臨時導出文件
      final tempDir = Directory.systemTemp;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final exportFile = File('${tempDir.path}/astreal_logs_$timestamp.txt');

      // 讀取主日誌文件內容
      final logContent = await logFile.readAsString();

      // 獲取崩潰報告
      final crashReports = _prefs.getStringList(_crashReportsKey) ?? [];

      // 獲取性能日誌
      final performanceLogs = _prefs.getStringList(_performanceLogsKey) ?? [];

      // 組合所有日誌內容
      final exportContent = StringBuffer();

      // 添加標題
      exportContent.writeln('# AstReal App 日誌導出');
      exportContent.writeln('# 導出時間: ${DateTime.now().toIso8601String()}');
      exportContent.writeln('# =====================================\n');

      // 添加主日誌
      exportContent.writeln('## 主要日誌');
      exportContent.writeln(logContent);
      exportContent.writeln('\n');

      // 添加崩潰報告
      if (crashReports.isNotEmpty) {
        exportContent.writeln('## 崩潰報告 (${crashReports.length} 個)');
        for (int i = 0; i < crashReports.length; i++) {
          exportContent.writeln('### 崩潰報告 ${i + 1}');
          exportContent.writeln(crashReports[i]);
          exportContent.writeln('');
        }
        exportContent.writeln('');
      }

      // 添加性能日誌
      if (performanceLogs.isNotEmpty) {
        exportContent.writeln('## 性能日誌 (${performanceLogs.length} 個)');
        for (int i = 0; i < performanceLogs.length; i++) {
          exportContent.writeln('### 性能日誌 ${i + 1}');
          exportContent.writeln(performanceLogs[i]);
          exportContent.writeln('');
        }
      }

      // 寫入導出文件
      await exportFile.writeAsString(exportContent.toString());

      logger.i('日誌導出成功: ${exportFile.path}');
      return exportFile;

    } catch (e) {
      logger.e('導出日誌失敗: $e');
      return null;
    }
  }
}

/// 日誌管理設定
class LogManagementSettings {
  bool autoUpload;
  bool autoUploadCrashes;
  bool enablePerformanceLogs;
  int maxLogFileSize;
  int logRetentionDays;
  DateTime? lastUploadTime;
  
  LogManagementSettings({
    this.autoUpload = false,
    this.autoUploadCrashes = false,
    this.enablePerformanceLogs = false,
    this.maxLogFileSize = 5 * 1024 * 1024, // 5MB
    this.logRetentionDays = 7,
    this.lastUploadTime,
  });
  
  factory LogManagementSettings.fromJson(Map<String, dynamic> json) {
    return LogManagementSettings(
      autoUpload: json['autoUpload'] ?? false,
      autoUploadCrashes: json['autoUploadCrashes'] ?? true,
      enablePerformanceLogs: json['enablePerformanceLogs'] ?? true,
      maxLogFileSize: json['maxLogFileSize'] ?? 5 * 1024 * 1024,
      logRetentionDays: json['logRetentionDays'] ?? 7,
      lastUploadTime: json['lastUploadTime'] != null 
          ? DateTime.parse(json['lastUploadTime']) 
          : null,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'autoUpload': autoUpload,
      'autoUploadCrashes': autoUploadCrashes,
      'enablePerformanceLogs': enablePerformanceLogs,
      'maxLogFileSize': maxLogFileSize,
      'logRetentionDays': logRetentionDays,
      'lastUploadTime': lastUploadTime?.toIso8601String(),
    };
  }
}

/// 日誌統計信息
class LogStatistics {
  final int localLogFileSize;
  final int uploadedLogsCount;
  final int pendingCrashReports;
  final int pendingPerformanceLogs;
  final DateTime? lastUploadTime;
  
  LogStatistics({
    this.localLogFileSize = 0,
    this.uploadedLogsCount = 0,
    this.pendingCrashReports = 0,
    this.pendingPerformanceLogs = 0,
    this.lastUploadTime,
  });
}
