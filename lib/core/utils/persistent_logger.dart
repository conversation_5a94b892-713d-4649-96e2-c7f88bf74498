import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../shared/utils/logger_utils.dart';

/// 持久化日誌管理器
/// 支援本地文件存儲和 Firebase Storage 上傳
class PersistentLogger {
  static PersistentLogger? _instance;
  static PersistentLogger get instance => _instance ??= PersistentLogger._();
  
  PersistentLogger._();
  
  late final Logger _logger;
  late final File _logFile;
  late final SharedPreferences _prefs;
  
  bool _initialized = false;
  final List<LogEntry> _logBuffer = [];
  Timer? _flushTimer;
  static const int _maxBufferSize = 50; // 減少緩衝區大小，避免過多文件操作
  static const int _maxLogFileSize = 5 * 1024 * 1024; // 5MB
  static const String _logFileName = 'astreal_app.log';
  static const String _uploadedLogsKey = 'uploaded_logs';
  
  /// 初始化日誌系統
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();

      // 不再需要 Logger，直接處理日誌條目

      // 初始化日誌文件
      await _initializeLogFile();

      // 檢查日誌文件是否成功初始化
      try {
        final _ = _logFile.path; // 測試 _logFile 是否已初始化
      } catch (e) {
        logger.e('日誌文件初始化失敗，無法啟動完整的日誌系統: $e');
        return; // 不設置 _initialized = true
      }

      // 清理舊日誌
      await _cleanupOldLogs();

      _initialized = true;

      // 啟動定時刷新器，每2分鐘刷新一次緩衝區
      _flushTimer = Timer.periodic(const Duration(minutes: 5), (_) {
        unawaited(_flushBuffer());
      });

      // 記錄初始化成功
      i('PersistentLogger 初始化成功');
    } catch (e) {
      logger.e('PersistentLogger 初始化失敗: $e');
    }
  }

  /// 初始化日誌文件
  Future<void> _initializeLogFile() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logDir = Directory('${directory.path}/logs');

      // 確保日誌目錄存在
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }

      _logFile = File('${logDir.path}/$_logFileName');

      // 如果文件不存在，創建它
      if (!await _logFile.exists()) {
        await _logFile.create();
        await _logFile.writeAsString(
            '# AstReal App Logs\n# Created: ${DateTime.now().toIso8601String()}\n\n',
            encoding: utf8);
      }

      // 檢查文件大小，如果太大則輪轉
      await _rotateLogFileIfNeeded();
    } catch (e) {
      logger.e('初始化日誌文件失敗: $e');
    }
  }

  /// 輪轉日誌文件（如果文件太大）
  Future<void> _rotateLogFileIfNeeded() async {
    try {
      // 檢查 _logFile 是否已初始化
      try {
        final _ = _logFile.path;
      } catch (e) {
        logger.e('日誌文件尚未初始化，跳過輪轉檢查: $e');
        return;
      }

      final stat = await _logFile.stat();
      if (stat.size > _maxLogFileSize) {
        // 備份當前日誌文件
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final backupFile = File('${_logFile.path}.$timestamp.bak');
        await _logFile.copy(backupFile.path);

        // 清空當前日誌文件
        await _logFile.writeAsString(
            '# AstReal App Logs (Rotated)\n# Created: ${DateTime.now().toIso8601String()}\n\n');

        // 上傳備份文件到 Firebase Storage
        // TODO
        // _uploadLogFileToFirebase(backupFile);
      }
    } catch (e) {
      logger.e('日誌文件輪轉失敗: $e');
    }
  }

  /// 清理舊日誌文件
  Future<void> _cleanupOldLogs() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logDir = Directory('${directory.path}/logs');

      if (await logDir.exists()) {
        final files = await logDir.list().toList();
        final now = DateTime.now();

        for (final file in files) {
          if (file is File && file.path.endsWith('.bak')) {
            final stat = await file.stat();
            final age = now.difference(stat.modified);

            // 刪除超過 7 天的備份文件
            if (age.inDays > 7) {
              await file.delete();
            }
          }
        }
      }
    } catch (e) {
      logger.e('清理舊日誌失敗: $e');
    }
  }

  /// Debug 級別日誌
  void d(String message, [dynamic error, StackTrace? stackTrace]) {
    _writeLogEntry(Level.debug, message, error, stackTrace);
  }

  /// Info 級別日誌
  void i(String message, [dynamic error, StackTrace? stackTrace]) {
    _writeLogEntry(Level.info, message, error, stackTrace);
  }

  /// Warning 級別日誌
  void w(String message, [dynamic error, StackTrace? stackTrace]) {
    _writeLogEntry(Level.warning, message, error, stackTrace);
  }

  /// Error 級別日誌
  void e(String message, [dynamic error, StackTrace? stackTrace]) {
    _writeLogEntry(Level.error, message, error, stackTrace);
  }

  /// 直接寫入日誌條目，避免通過 Logger 的複雜處理鏈
  void _writeLogEntry(Level level, String message,
      [dynamic error, StackTrace? stackTrace]) {
    if (!_initialized) {
      if (!kReleaseMode) {
        print('[${level.name.toUpperCase()}] $message');
      }
      return;
    }

    final entry = LogEntry(
      timestamp: DateTime.now(),
      level: level,
      message: message,
      error: error,
      stackTrace: stackTrace,
    );

    _writeToFile(entry);
  }

  /// 寫入日誌到文件
  Future<void> _writeToFile(LogEntry entry) async {
    try {
      // 檢查是否已初始化，如果沒有則跳過文件寫入
      if (!_initialized) {
        print('PersistentLogger 尚未初始化，跳過文件寫入');
        return;
      }

      // 添加到緩衝區
      _logBuffer.add(entry);

      // 只有在緩衝區滿了或者是錯誤級別時才立即刷新
      if (_logBuffer.length >= _maxBufferSize || entry.level == Level.error) {
        // 使用 unawaited 避免阻塞當前操作，減少文件操作頻率
        unawaited(_flushBuffer());
      }
    } catch (e) {
      if (!kReleaseMode) {
        print('[寫入日誌文件失敗] $e');
      }
    }
  }

  /// 刷新緩衝區到文件
  Future<void> _flushBuffer() async {
    // 檢查初始化狀態和緩衝區
    if (!_initialized || _logBuffer.isEmpty) {
      if (!_initialized) {
        if (!kReleaseMode) {
          print('PersistentLogger 尚未初始化，跳過緩衝區刷新');
        }
      }
      return;
    }

    // 檢查 _logFile 是否已初始化
    try {
      // 嘗試訪問 _logFile，如果未初始化會拋出 LateInitializationError
      final _ = _logFile.path;
    } catch (e) {
      if (!kReleaseMode) {
        print('日誌文件尚未初始化，跳過緩衝區刷新: $e');
      }
      return;
    }

    // 創建緩衝區副本並立即清空，避免在寫入過程中被修改
    final bufferCopy = List<LogEntry>.from(_logBuffer);
    _logBuffer.clear();

    try {
      // 確保日誌文件存在
      if (!await _logFile.exists()) {
        await _logFile.create(recursive: true);
      }

      // 使用 IOSink 確保正確關閉文件句柄，並明確指定 UTF-8 編碼
      final sink = _logFile.openWrite(mode: FileMode.append);
      try {
        for (final entry in bufferCopy) {
          sink.writeln(entry.toString());
        }
        await sink.flush();
      } finally {
        await sink.close(); // 確保文件句柄被正確關閉
      }

      // 檢查是否需要輪轉文件
      await _rotateLogFileIfNeeded();
    } catch (e) {
      // 如果寫入失敗，將資料放回緩衝區（但限制大小避免無限增長）
      if (_logBuffer.length < _maxBufferSize) {
        _logBuffer.insertAll(
            0, bufferCopy.take(_maxBufferSize - _logBuffer.length));
      }
      if (!kReleaseMode) {
        print('刷新日誌緩衝區失敗: $e');
      }
    }
  }

  /// 手動上傳當前日誌文件到 Firebase Storage
  Future<bool> uploadCurrentLogFile() async {
    try {
      if (!_initialized) {
        w('日誌系統未初始化，無法上傳');
        return false;
      }

      // 檢查 _logFile 是否已初始化
      try {
        final _ = _logFile.path;
      } catch (e) {
        w('日誌文件尚未初始化，無法上傳: $e');
        return false;
      }

      // 先刷新緩衝區
      await _flushBuffer();

      return await _uploadLogFileToFirebase(_logFile);
    } catch (e) {
      this.e('手動上傳日誌文件失敗: $e');
      return false;
    }
  }

  /// 上傳日誌文件到 Firebase Storage
  Future<bool> _uploadLogFileToFirebase(File logFile) async {
    try {
      // 檢查用戶是否已登入
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        w('用戶未登入，無法上傳日誌到 Firebase Storage');
        return false;
      }

      // 檢查文件是否存在且不為空
      if (!await logFile.exists()) {
        w('日誌文件不存在，無法上傳');
        return false;
      }

      final stat = await logFile.stat();
      if (stat.size == 0) {
        w('日誌文件為空，跳過上傳');
        return false;
      }

      i('開始上傳日誌文件到 Firebase Storage，文件大小: ${stat.size} bytes');

      // 生成唯一的文件名
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'app_log_$timestamp.log';
      final storagePath = 'user_logs/${currentUser.uid}/$fileName';

      // String content = await logFile.readAsString();
      // print('上傳日誌文件內容: $content');

      // 讀取文件內容
      final logContent = await logFile.readAsBytes();

      // 創建 Firebase Storage 引用
      final storageRef = FirebaseStorage.instance.ref().child(storagePath);

      // 設置元數據
      final metadata = SettableMetadata(
        contentType: 'text/plain; charset=utf-8', // ; charset=utf-8 避免亂碼
        customMetadata: {
          'app_version': '1.0.0', // 可以從 package_info 獲取
          'platform': kIsWeb ? 'web' : Platform.operatingSystem,
          'upload_time': DateTime.now().toIso8601String(),
          'file_size': stat.size.toString(),
          'log_type': logFile.path.contains('.bak') ? 'rotated' : 'current',
        },
      );

      // 上傳文件
      final uploadTask = storageRef.putData(logContent, metadata);
      final snapshot = await uploadTask;

      if (snapshot.state == TaskState.success) {
        i('日誌文件上傳成功: $storagePath');

        // 記錄已上傳的日誌
        await _recordUploadedLog(fileName, storagePath, stat.size);

        return true;
      } else {
        this.e('日誌文件上傳失敗: ${snapshot.state}');
        return false;
      }
    } catch (e) {
      logger.e('上傳日誌文件到 Firebase Storage 失敗: $e');
      return false;
    }
  }

  /// 記錄已上傳的日誌信息
  Future<void> _recordUploadedLog(
      String fileName, String storagePath, int fileSize) async {
    try {
      final uploadedLogs = _prefs.getStringList(_uploadedLogsKey) ?? [];

      final logInfo = {
        'fileName': fileName,
        'storagePath': storagePath,
        'fileSize': fileSize,
        'uploadTime': DateTime.now().toIso8601String(),
      };

      uploadedLogs.add(jsonEncode(logInfo));

      // 只保留最近 50 條上傳記錄
      if (uploadedLogs.length > 50) {
        uploadedLogs.removeRange(0, uploadedLogs.length - 50);
      }

      await _prefs.setStringList(_uploadedLogsKey, uploadedLogs);
    } catch (e) {
      if (!kReleaseMode) {
        print('記錄已上傳日誌信息失敗: $e');
      }
    }
  }

  /// 獲取已上傳的日誌列表
  List<Map<String, dynamic>> getUploadedLogs() {
    try {
      final uploadedLogs = _prefs.getStringList(_uploadedLogsKey) ?? [];
      return uploadedLogs
          .map((log) => jsonDecode(log) as Map<String, dynamic>)
          .toList();
    } catch (e) {
      this.e('獲取已上傳日誌列表失敗: $e');
      return [];
    }
  }

  /// 清理本地日誌文件
  Future<void> clearLocalLogs() async {
    try {
      if (!_initialized) {
        if (!kReleaseMode) {
          print('日誌系統未初始化，無法清理');
        }
        return;
      }

      // 檢查 _logFile 是否已初始化
      try {
        final _ = _logFile.path;
      } catch (e) {
        if (!kReleaseMode) {
          print('日誌文件尚未初始化，無法清理: $e');
        }
        return;
      }

      _logBuffer.clear();
      await _logFile.writeAsString(
          '# AstReal App Logs (Cleared)\n# Created: ${DateTime.now().toIso8601String()}\n\n');
      i('本地日誌文件已清理');
    } catch (e) {
      this.e('清理本地日誌文件失敗: $e');
    }
  }

  /// 獲取當前日誌文件大小
  Future<int> getLogFileSize() async {
    try {
      if (!_initialized) {
        return 0;
      }

      // 檢查 _logFile 是否已初始化
      try {
        final _ = _logFile.path;
      } catch (e) {
        return 0;
      }

      if (await _logFile.exists()) {
        final stat = await _logFile.stat();
        return stat.size;
      }
      return 0;
    } catch (e) {
      this.e('獲取日誌文件大小失敗: $e');
      return 0;
    }
  }

  /// 獲取當前日誌文件（供外部使用）
  File? getCurrentLogFile() {
    try {
      if (!_initialized) {
        return null;
      }

      // 檢查 _logFile 是否已初始化
      try {
        final _ = _logFile.path;
        return _logFile;
      } catch (e) {
        return null;
      }
    } catch (e) {
      this.e('獲取日誌文件失敗: $e');
      return null;
    }
  }

  /// 刷新緩衝區（供外部使用）
  Future<void> flushBuffer() async {
    await _flushBuffer();
  }

  /// 應用程式關閉時調用，確保日誌被保存
  Future<void> dispose() async {
    try {
      // 取消定時器
      _flushTimer?.cancel();
      _flushTimer = null;

      if (_initialized) {
        await _flushBuffer();
        i('PersistentLogger 已關閉');
      }
    } catch (e) {
      if (!kReleaseMode) {
        print('PersistentLogger 關閉失敗: $e');
      }
    }
  }
}

/// 日誌條目模型
class LogEntry {
  final DateTime timestamp;
  final Level level;
  final String message;
  final dynamic error;
  final StackTrace? stackTrace;

  LogEntry({
    required this.timestamp,
    required this.level,
    required this.message,
    this.error,
    this.stackTrace,
  });

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.write('[${timestamp.toIso8601String()}] ');
    buffer.write('[${level.name.toUpperCase()}] ');
    buffer.write(message);

    if (error != null) {
      buffer.write(' | Error: $error');
    }

    if (stackTrace != null) {
      buffer.write('\nStackTrace:\n$stackTrace');
    }

    return buffer.toString();
  }
}
