import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../astreal.dart';
import '../../../data/services/api/auth_service.dart';
import '../../../data/services/api/hidden_announcement_service.dart';
import '../../../data/services/api/interpretation_credits_service.dart';
import '../../../shared/utils/url_launcher_service.dart';
import '../../../shared/utils/user_preferences.dart';
import '../../../shared/widgets/common/responsive_wrapper.dart';
import '../../../shared/widgets/unified_card.dart';
import '../about_us_page.dart';
import '../admin/admin_dashboard_page.dart';
import '../daily_astrology_settings_page.dart';
import '../firebase_login_page.dart';
import '../onboarding/user_mode_selection_page.dart';
import '../settings/ai_model_settings_page.dart';
import '../settings/api_key_settings_page.dart';
import '../settings/astrology_guidance_page.dart';
import '../settings/chart_display_settings_page.dart';
import '../settings/interpretation_guidance_settings_page.dart';
import '../settings/feedback_page.dart';
import '../settings/log_management_page.dart';
import '../settings/purchase_interpretation_page.dart';
import '../settings/system_settings_page.dart';
import '../user_profile_page.dart';
import '../video_list_page.dart';

/// 設置頁面 - 主入口頁面，提供導航到各個設置子頁面
class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  // 判斷是否為開發環境
  bool get _isInDevelopmentMode {
    return kDebugMode || kProfileMode;
  }

  // 用戶解讀次數狀態
  bool _isPremium = false;
  int _totalCredits = 0;
  bool _isLoadingStatus = true;

  // 用戶模式狀態
  String _userMode = 'starmaster';
  bool _isLoadingMode = true;

  // 用戶檔案狀態
  UserProfile? _userProfile;
  bool _isLoadingUserProfile = true;

  // 隱藏公告狀態
  int _hiddenAnnouncementsCount = 0;
  bool _isLoadingHiddenAnnouncements = true;

  // 流式模式狀態
  bool _streamingMode = true;
  bool _isLoadingStreamingMode = true;

  @override
  void initState() {
    super.initState();
    _loadUserStatus();
    _loadHiddenAnnouncementsStats();
  }

  /// 刷新所有狀態（登入成功後調用）
  Future<void> _refreshAllStates() async {
    logger.i('刷新設定頁面所有狀態');
    await Future.wait([
      _loadUserStatus(),
      _loadHiddenAnnouncementsStats(),
    ]);
  }

  /// 載入用戶狀態
  Future<void> _loadUserStatus() async {
    try {
      logger.d('開始載入用戶狀態');

      // 使用新的統一解讀次數服務
      final creditsDetails =
          await InterpretationCreditsService.getCreditsDetails();

      // 載入用戶模式
      final prefs = await SharedPreferences.getInstance();
      final userMode = prefs.getString('user_mode') ?? 'starmaster';

      // 載入用戶檔案
      UserProfile? userProfile;
      final currentUser = AuthService.getCurrentUser();
      if (currentUser != null) {
        logger.d('當前用戶: ${currentUser.uid}');
        userProfile =
            await UserProfileUnifiedService.getUserById(currentUser.uid);

        // 記錄管理者狀態
        if (userProfile?.isAdmin == true) {
          logger.i('檢測到管理者用戶: ${currentUser.uid}');
        }
      } else {
        logger.d('未檢測到登入用戶');
      }

      // 載入流式模式設定
      final streamingMode = await UserPreferences.getStreamingMode();

      if (mounted) {
        setState(() {
          _isPremium = creditsDetails['isPremium'] ?? false;
          _totalCredits = creditsDetails['totalCredits'] ?? 0;
          _isLoadingStatus = false;
          _userMode = userMode;
          _isLoadingMode = false;
          _userProfile = userProfile;
          _isLoadingUserProfile = false;
          _streamingMode = streamingMode;
          _isLoadingStreamingMode = false;
        });

        // 記錄狀態更新完成
        logger.d('用戶狀態載入完成，管理者狀態: ${userProfile?.isAdmin}');
      }
    } catch (e) {
      logger.e('載入用戶狀態失敗: $e');
      if (mounted) {
        setState(() {
          _isLoadingStatus = false;
          _isLoadingMode = false;
          _isLoadingUserProfile = false;
          _isLoadingStreamingMode = false;
        });
      }
    }
  }

  /// 載入隱藏公告統計
  Future<void> _loadHiddenAnnouncementsStats() async {
    try {
      final stats =
          await HiddenAnnouncementService.getHiddenAnnouncementsStats();

      if (mounted) {
        setState(() {
          _hiddenAnnouncementsCount = stats['totalHidden'] ?? 0;
          _isLoadingHiddenAnnouncements = false;
        });
      }
    } catch (e) {
      logger.e('載入隱藏公告統計失敗: $e');
      if (mounted) {
        setState(() {
          _isLoadingHiddenAnnouncements = false;
        });
      }
    }
  }

  /// 還原所有已隱藏的公告
  Future<void> _restoreAllHiddenAnnouncements() async {
    try {
      // 顯示確認對話框
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('確認還原'),
          content: Text(
            '確定要還原所有已隱藏的公告嗎？\n\n'
            '目前共有 $_hiddenAnnouncementsCount 個已隱藏的公告。'
            '還原後，這些公告將重新顯示。',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
              ),
              child: const Text('確認還原'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      // 執行還原操作
      final success =
          await HiddenAnnouncementService.restoreAllHiddenAnnouncements();

      if (mounted) {
        if (success) {
          // 更新統計
          setState(() {
            _hiddenAnnouncementsCount = 0;
          });

          // 顯示成功訊息
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('已成功還原所有隱藏的公告'),
              backgroundColor: AppColors.success,
              behavior: SnackBarBehavior.floating,
            ),
          );
        } else {
          // 顯示錯誤訊息
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('還原隱藏公告失敗，請稍後再試'),
              backgroundColor: AppColors.error,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      logger.e('還原隱藏公告失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('還原隱藏公告失敗: $e'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<SettingsViewModel, AuthViewModel, ThemeProvider>(
      builder: (context, settingsViewModel, authViewModel, themeProvider, _) {
        final isStarlight = themeProvider.userMode == 'starlight';
        final primaryColor =
            isStarlight ? AppColors.solarAmber : AppColors.royalIndigo;
        final backgroundColor =
            isStarlight ? AppColors.lightCornsilk : AppColors.pastelSkyBlue;

        if (settingsViewModel.isLoading) {
          return Scaffold(
            backgroundColor: backgroundColor,
            body: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
              ),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(
              '設定',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            backgroundColor: backgroundColor,
            iconTheme: IconThemeData(color: primaryColor),
            elevation: 0,
          ),
          backgroundColor: backgroundColor,
          body: ResponsivePageWrapper(
            maxWidth: 800.0, // 設定頁面適合中等寬度
            child: CustomScrollView(
              slivers: [
                SliverPadding(
                  padding: const EdgeInsets.fromLTRB(16, 20, 16, 16),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      // 用戶認證區塊
                      _buildAuthSection(authViewModel),
                      const SizedBox(height: 20),

                      // 購買解讀次數
                      _buildPurchaseCard(),
                      const SizedBox(height: 20),

                      // 功能設置分組
                      _buildSectionHeader('功能設置'),
                      const SizedBox(height: 12),

                      // 用戶模式切換
                      _buildUserModeCard(),
                      const SizedBox(height: 12),

                      // 星盤顯示設置（包含相位設置）
                      _buildSettingCard(
                        title: '星盤顯示設置',
                        subtitle: '自定義星盤的顯示方式、行星顯示、相位設定等',
                        icon: Icons.auto_awesome,
                        color: AppColors.royalIndigo,
                        onTap: () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const ChartDisplaySettingsPage(),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),

                      // 每日星相推播設置
                      _buildSettingCard(
                        title: '每日星相推播',
                        subtitle: '設定每日星象提醒、推播時間和個人化內容',
                        icon: Icons.notifications_outlined,
                        color: AppColors.solarAmber,
                        onTap: () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const DailyAstrologySettingsPage(),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),



                      // 占星指引分組
                      _buildSectionHeader('占星指引'),
                      const SizedBox(height: 12),

                      // 占星分析注意事項
                      _buildSettingCard(
                        title: '占星分析注意事項',
                        subtitle: '了解占星分析的使用原則、準確性說明和重要提醒',
                        icon: Icons.info_outline,
                        color: AppColors.warning,
                        onTap: () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AstrologyGuidancePage(),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),

                      // 願景理念
                      _buildSettingCard(
                        title: '願景理念',
                        subtitle: '科技占星 —— 理性與直覺',
                        icon: Icons.lightbulb_outline,
                        color: AppColors.cosmicPurple,
                        onTap: () => UrlLauncherService.launchURL(
                          'https://astreal-website.web.app/vision.html',
                          context: context,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // 通知管理分組
                      _buildSectionHeader('通知管理'),
                      const SizedBox(height: 12),

                      // 隱藏公告管理
                      _buildHiddenAnnouncementsCard(),

                      // 管理者選項（僅管理者可見）
                      if (!_isLoadingUserProfile &&
                          _userProfile?.isAdmin == true) ...[
                        const SizedBox(height: 20),
                        _buildSectionHeader('管理者選項'),
                        const SizedBox(height: 12),

                        // 管理後台
                        _buildSettingCard(
                          title: '管理後台',
                          subtitle: '系統管理與監控中心',
                          icon: Icons.admin_panel_settings,
                          color: AppColors.royalIndigo,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const AdminDashboardPage(),
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),

                        // 重置首次啟動狀態
                        _buildSettingCard(
                          title: '重置首次啟動',
                          subtitle: '重置應用介紹頁面顯示狀態（測試用）',
                          icon: Icons.refresh,
                          color: Colors.orange,
                          onTap: () => _resetFirstTimeUserStatus(),
                        ),
                        const SizedBox(height: 12),

                        _buildSectionHeader('開發者選項'),
                        const SizedBox(height: 12),

                        // 日誌管理
                        _buildSettingCard(
                          title: '日誌管理',
                          subtitle: '查看應用程式日誌、上傳診斷資料和管理日誌設定',
                          icon: Icons.bug_report,
                          color: Colors.green,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const LogManagementPage(),
                            ),
                          ),
                        ),

                        const SizedBox(height: 12),
                        // 解讀指引設定
                        _buildSettingCard(
                          title: '解讀指引設定',
                          subtitle: '自定義 AI 解讀的指引內容，支援 Remote Config 和自定義指引',
                          icon: Icons.psychology_outlined,
                          color: AppColors.solarAmber,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const InterpretationGuidanceSettingsPage(),
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),

                        // AI 即時顯示模式
                        _buildStreamingModeItem(),
                        const SizedBox(height: 12),

                        _buildSettingCard(
                          title: '模型設置',
                          subtitle: '選擇用於星盤解讀的模型',
                          icon: Icons.psychology,
                          color: AppColors.solarAmber,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const AIModelSettingsPage(),
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),

                        _buildSettingCard(
                          title: 'API Key 設置',
                          subtitle: '設置 OpenAI 和 Anthropic 的 API Key',
                          icon: Icons.key,
                          color: Colors.orange,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const ApiKeySettingsPage(),
                            ),
                          ),
                        ),
                      ],

                      const SizedBox(height: 20),

                      // 其他設置分組
                      _buildSectionHeader('其他設置'),
                      const SizedBox(height: 12),

                      // 問題回饋
                      _buildSettingCard(
                        title: '問題回饋',
                        subtitle: '回報錯誤、建議功能或其他問題',
                        icon: Icons.feedback,
                        color: AppColors.solarAmber,
                        onTap: () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const FeedbackPage(),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),

                      // 系統設置
                      _buildSettingCard(
                        title: '系統設置',
                        subtitle: '資料管理、應用偏好和關於信息',
                        icon: Icons.settings,
                        color: Colors.grey.shade600,
                        onTap: () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SystemSettingsPage(),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),

                      // 法律條款區塊
                      _buildLegalSection(),
                      const SizedBox(height: 20),
                    ]),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 構建分組標題
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 4, bottom: 4),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.grey.shade700,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  /// 構建認證區塊
  Widget _buildAuthSection(AuthViewModel authViewModel) {
    final user = authViewModel.currentUser;
    final isAuthenticated = authViewModel.isAuthenticated;
    final isAuthenticating = authViewModel.isAuthenticating;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: isAuthenticated
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    isAuthenticated
                        ? Icons.account_circle
                        : Icons.account_circle_outlined,
                    color: isAuthenticated ? Colors.green : Colors.grey,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isAuthenticated ? '已登入' : '未登入',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (isAuthenticated) ...[
                        Text(
                          user?.displayName ?? user?.email ?? '用戶',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        if (user?.isAnonymous == true)
                          Container(
                            margin: const EdgeInsets.only(top: 2),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.orange.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Text(
                              '匿名用戶',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.orange,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        if (user?.emailVerified == false &&
                            user?.email != null &&
                            user?.isAnonymous != true)
                          Container(
                            margin: const EdgeInsets.only(top: 2),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.red.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Text(
                              '郵件未驗證',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ] else ...[
                        Text(
                          '登入以同步您的資料和設定',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (isAuthenticated) ...[
                  // 用戶管理按鈕
                  PopupMenuButton<String>(
                    onSelected: (value) =>
                        _handleUserAction(value, authViewModel),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'profile',
                        child: Row(
                          children: [
                            Icon(Icons.person),
                            SizedBox(width: 8),
                            Text('用戶資料'),
                          ],
                        ),
                      ),
                      if (user?.emailVerified == false &&
                          user?.isAnonymous != true)
                        const PopupMenuItem(
                          value: 'verify_email',
                          child: Row(
                            children: [
                              Icon(Icons.email),
                              SizedBox(width: 8),
                              Text('驗證郵件'),
                            ],
                          ),
                        ),
                      const PopupMenuItem(
                        value: 'logout',
                        child: Row(
                          children: [
                            Icon(Icons.logout),
                            SizedBox(width: 8),
                            Text('登出'),
                          ],
                        ),
                      ),
                    ],
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.royalIndigo),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text('管理'),
                          SizedBox(width: 4),
                          Icon(Icons.arrow_drop_down, size: 16),
                        ],
                      ),
                    ),
                  ),
                ] else ...[
                  // 登入按鈕
                  if (isAuthenticating)
                    const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  else
                    ElevatedButton(
                      onPressed: () => _navigateToLogin(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.royalIndigo,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('登入'),
                    ),
                ],
              ],
            ),
            if (isAuthenticated &&
                user?.emailVerified == false &&
                user?.isAnonymous != true) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.warning_amber,
                      color: Colors.orange,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        '請驗證您的電子郵件地址',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.orange,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () => _resendEmailVerification(authViewModel),
                      child: const Text(
                        '重新發送',
                        style: TextStyle(color: Colors.orange),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 導航到登入頁面
  void _navigateToLogin() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FirebaseLoginPage(),
      ),
    ).then((result) {
      // 登入成功後重新載入用戶狀態
      if (result != null) {
        logger.i('登入成功，重新載入設定頁面狀態');
        _refreshAllStates();
      }
    });
  }

  /// 處理用戶操作
  void _handleUserAction(String action, AuthViewModel authViewModel) {
    switch (action) {
      case 'profile':
        _navigateToUserProfile();
        break;
      case 'verify_email':
        _sendEmailVerification(authViewModel);
        break;
      case 'logout':
        _showLogoutDialog(authViewModel);
        break;
    }
  }

  /// 導航到用戶資料頁面
  void _navigateToUserProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const UserProfilePage(),
      ),
    ).then((_) {
      // 從用戶資料頁面返回後刷新狀態
      logger.d('從用戶資料頁面返回，刷新設定頁面狀態');
      _refreshAllStates();
    });
  }

  /// 發送電子郵件驗證
  void _sendEmailVerification(AuthViewModel authViewModel) async {
    try {
      final success = await authViewModel.sendEmailVerification();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? '驗證郵件已發送' : '發送失敗'),
            backgroundColor: success ? AppColors.successGreen : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('發送失敗：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 顯示登出確認對話框
  void _showLogoutDialog(AuthViewModel authViewModel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('確認登出'),
        content: const Text('您確定要登出嗎？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();

              // 執行登出
              await authViewModel.signOut();

              // 登出後刷新狀態
              logger.i('登出成功，重新載入設定頁面狀態');
              _refreshAllStates();
            },
            child: const Text('登出'),
          ),
        ],
      ),
    );
  }

  /// 重新發送電子郵件驗證
  void _resendEmailVerification(AuthViewModel authViewModel) async {
    final success = await authViewModel.sendEmailVerification();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? '驗證郵件已發送' : '發送失敗，請稍後再試'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  /// 構建設置卡片
  Widget _buildSettingCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.12),
                    borderRadius: BorderRadius.circular(14),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textDark,
                          letterSpacing: 0.2,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                          height: 1.4,
                          letterSpacing: 0.1,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.08),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: color,
                    size: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建購買解讀次數卡片
  Widget _buildPurchaseCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: () async {
            final result = await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const PurchaseInterpretationPage(),
              ),
            );
            if (result == true) {
              _loadUserStatus();
            }
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.account_balance_wallet,
                      color: AppColors.royalIndigo,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '解讀服務',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textDark,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _isPremium ? '付費會員 - 無限次解讀' : '確認可用解讀次數',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: AppColors.royalIndigo.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.arrow_forward_ios,
                        color: AppColors.royalIndigo,
                        size: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (_isLoadingStatus) ...[
                  const Center(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
                ] else if (_isPremium) ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.successGreen.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.successGreen.withOpacity(0.3),
                      ),
                    ),
                    child: const Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: AppColors.successGreen,
                          size: 18,
                        ),
                        SizedBox(width: 8),
                        Text(
                          '付費會員 - 無限次解讀',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColors.successGreen,
                          ),
                        ),
                      ],
                    ),
                  ),
                ] else ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                        vertical: 14, horizontal: 16),
                    decoration: BoxDecoration(
                      color: AppColors.royalIndigo.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.royalIndigo.withOpacity(0.2),
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.account_balance_wallet,
                          color: AppColors.royalIndigo,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          '可用解讀次數',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.royalIndigo,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          '$_totalCredits 次',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.royalIndigo,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 構建 AI 即時顯示模式設定項目
  Widget _buildStreamingModeItem() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.royalIndigo.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            Icons.flash_on,
            color: AppColors.royalIndigo,
            size: 24,
          ),
        ),
        title: const Text(
          '分析內容即時顯示',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.textDark,
          ),
        ),
        subtitle: Text(
          _streamingMode ? '開啟後回應會逐字顯示' : '關閉後回應會一次性顯示完整內容',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
            height: 1.3,
          ),
        ),
        trailing: _isLoadingStreamingMode
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : Switch(
                value: _streamingMode,
                onChanged: _toggleStreamingMode,
                activeColor: AppColors.royalIndigo,
                activeTrackColor: AppColors.royalIndigo.withOpacity(0.3),
              ),
      ),
    );
  }

  /// 構建用戶模式切換卡片
  Widget _buildUserModeCard() {
    final isStarlight = _userMode == 'starlight';
    final modeColor =
        isStarlight ? AppColors.solarAmber : AppColors.royalIndigo;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: modeColor.withValues(alpha: 0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: modeColor.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _showUserModeDialog,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 52,
                      height: 52,
                      decoration: BoxDecoration(
                        color: modeColor.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        isStarlight ? Icons.star_outline : Icons.star,
                        color: modeColor,
                        size: 26,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '使用模式',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textDark,
                              letterSpacing: 0.3,
                            ),
                          ),
                          const SizedBox(height: 6),
                          if (_isLoadingMode) ...[
                            const SizedBox(
                              width: 100,
                              height: 14,
                              child: LinearProgressIndicator(),
                            ),
                          ] else ...[
                            Text(
                              isStarlight
                                  ? 'Starlight (初心者模式)'
                                  : 'Starmaster (占星師模式)',
                              style: TextStyle(
                                fontSize: 15,
                                color: modeColor,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 0.2,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: modeColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.swap_horiz,
                        color: modeColor,
                        size: 18,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                if (!_isLoadingMode) ...[
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: modeColor.withValues(alpha: 0.08),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: modeColor.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      isStarlight ? '當前使用簡化界面，適合占星初學者' : '當前使用完整功能，適合專業占星師',
                      style: TextStyle(
                        fontSize: 13,
                        color: modeColor,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0.1,
                        height: 1.3,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 顯示用戶模式切換頁面
  Future<void> _showUserModeDialog() async {
    // 直接導航到模式選擇頁面，設置為模式切換模式
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const UserModeSelectionPage(isModeSwitch: true),
      ),
    );
    // 注意：不需要等待結果，因為 UserModeSelectionPage 會直接導航到主頁面
  }

  /// 顯示重新啟動對話框
  void _showRestartDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重新啟動應用'),
        content: const Text(
          '為了完全切換到新模式，建議您重新啟動應用。\n\n您可以關閉應用後重新開啟，或者繼續使用當前界面。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('稍後重啟'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // 這裡可以添加重新啟動應用的邏輯
              // 或者導航到主頁面
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
            ),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }

  /// 構建法律條款區塊
  Widget _buildLegalSection() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // const Row(
            //   children: [
            //     Icon(
            //       Icons.gavel,
            //       color: AppColors.textDark,
            //       size: 20,
            //     ),
            //     SizedBox(width: 8),
            //     Text(
            //       '法律條款',
            //       style: TextStyle(
            //         fontSize: 16,
            //         fontWeight: FontWeight.bold,
            //         color: AppColors.textDark,
            //       ),
            //     ),
            //   ],
            // ),
            // const SizedBox(height: 16),

            // 隱私權政策
            _buildLegalItem(
              title: '隱私權政策',
              subtitle: '了解我們如何保護您的個人資料',
              icon: Icons.privacy_tip,
              onTap: () => UrlLauncherService.launchURL(
                'https://astreal-website.web.app/privacy-policy.html',
                context: context,
              ),
            ),

            const SizedBox(height: 12),

            // 服務條款
            _buildLegalItem(
              title: '服務條款',
              subtitle: '查看使用本應用程式的條款與規定',
              icon: Icons.description,
              onTap: () => UrlLauncherService.launchURL(
                'https://astreal-website.web.app/terms-of-service.html',
                context: context,
              ),
            ),

            const SizedBox(height: 12),

            // 影片教學
            _buildLegalItem(
              title: '影片教學',
              subtitle: '觀看占星學習影片與應用教學',
              icon: Icons.video_library,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const VideoListPage(),
                ),
              ),
            ),

            const SizedBox(height: 12),

            // 關於我們
            _buildLegalItem(
              title: '關於我們',
              subtitle: '了解團隊與官方社群',
              icon: Icons.info,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AboutUsPage(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建法律條款項目
  Widget _buildLegalItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        child: Row(
          children: [
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: AppColors.royalIndigo,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.open_in_new,
              color: AppColors.royalIndigo,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  // 移除舊的 _launchURL 方法，現在使用 UrlLauncherService

  /// 構建隱藏公告管理卡片
  Widget _buildHiddenAnnouncementsCard() {
    return UnifiedCard(
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppColors.warning.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.visibility_off,
            color: AppColors.warning,
            size: 20,
          ),
        ),
        title: const Text(
          '隱藏公告管理',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: _isLoadingHiddenAnnouncements
            ? const Text('載入中...')
            : Text(
                _hiddenAnnouncementsCount > 0
                    ? '目前有 $_hiddenAnnouncementsCount 個已隱藏的公告'
                    : '沒有隱藏的公告',
                style: const TextStyle(fontSize: 14),
              ),
        trailing: _hiddenAnnouncementsCount > 0
            ? TextButton(
                onPressed: _restoreAllHiddenAnnouncements,
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.royalIndigo,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                ),
                child: const Text(
                  '還原全部',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              )
            : const Icon(
                Icons.check_circle_outline,
                color: AppColors.success,
                size: 20,
              ),
        onTap: _hiddenAnnouncementsCount > 0
            ? _restoreAllHiddenAnnouncements
            : null,
      ),
    );
  }

  /// 切換流式模式
  Future<void> _toggleStreamingMode(bool enabled) async {
    try {
      // 保存設定
      final success = await UserPreferences.saveStreamingMode(enabled);

      if (success) {
        setState(() {
          _streamingMode = enabled;
        });

        // 顯示成功提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                enabled ? '已開啟 AI 即時顯示模式' : '已關閉 AI 即時顯示模式',
              ),
              backgroundColor: AppColors.royalIndigo,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        // 保存失敗，恢復原狀態
        setState(() {
          _streamingMode = !enabled;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('設定保存失敗，請稍後再試'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      logger.e('切換流式模式失敗: $e');

      // 發生錯誤，恢復原狀態
      setState(() {
        _streamingMode = !enabled;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('設定更新失敗，請檢查網路連接'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// 重置首次啟動狀態
  Future<void> _resetFirstTimeUserStatus() async {
    try {
      // 顯示確認對話框
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('重置首次啟動狀態'),
          content: const Text('這將重置應用介紹頁面的顯示狀態，下次啟動應用時會重新顯示介紹頁面。\n\n確定要繼續嗎？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.orange,
              ),
              child: const Text('確定重置'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // 執行重置
        final success = await UserPreferences.resetFirstTimeUserStatus();

        if (mounted) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('首次啟動狀態已重置，下次啟動應用時會顯示介紹頁面'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 3),
              ),
            );
            logger.i('管理員重置首次啟動狀態成功');
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('重置失敗，請稍後再試'),
                backgroundColor: Colors.red,
                duration: Duration(seconds: 2),
              ),
            );
            logger.e('管理員重置首次啟動狀態失敗');
          }
        }
      }
    } catch (e) {
      logger.e('重置首次啟動狀態時發生錯誤: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('操作失敗，請檢查網路連接'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }
}
