# AI 解讀結果頁面 API 費用計算功能實作總結

## 📋 功能概述

為 `AIInterpretationResultPage` 實作了完整的 API 費用自動計算和顯示功能，讓用戶能夠清楚了解每次 AI 解讀的實際成本，並提供費用優化建議。

## 🎯 主要功能特色

### 1. 自動費用計算
- **即時計算**：解讀完成後自動計算 API 費用
- **精確估算**：基於實際的 prompt 和 response 內容計算 token 使用量
- **多貨幣支援**：支援美元 (USD) 和台幣 (TWD) 顯示
- **流式響應支援**：同時支援傳統和流式 AI 響應模式

### 2. 詳細費用分解
- **Token 統計**：顯示輸入、輸出和總計 token 數量
- **提供商資訊**：顯示當前使用的 AI 模型和定價資訊
- **費用明細**：分別顯示輸入和輸出的費用構成
- **快取支援**：支援 GPT-5 系列的快取輸入定價

### 3. 智能費用建議
- **節省提示**：比較不同模型的費用，提供節省建議
- **百分比顯示**：顯示可節省的金額和百分比
- **模型比較**：支援查看所有可用模型的費用對比

## 🏗️ 技術實作

### 核心服務：APICostCalculatorService

#### 最新定價模型（2025-01-09 更新）
```dart
/// OpenAI 模型定價表（每 1000 tokens 的費用，單位：美元）
static const Map<String, APIPricing> _openaiModelPricing = {
  'gpt-5': APIPricing(
    inputCostPer1000: 0.625,   // $0.625/1K tokens
    outputCostPer1000: 5.00,   // $5.00/1K tokens
    cachedInputCostPer1000: 0.0625, // $0.0625/1K tokens (快取)
  ),
  'gpt-4o': APIPricing(
    inputCostPer1000: 1.25,    // $1.25/1K tokens
    outputCostPer1000: 5.00,   // $5.00/1K tokens
  ),
  'gpt-4o-mini': APIPricing(
    inputCostPer1000: 0.075,   // $0.075/1K tokens
    outputCostPer1000: 0.30,   // $0.30/1K tokens
  ),
  // ... 更多模型
};
```

#### 智能費用計算
```dart
/// 計算具體模型的 API 費用
static double calculateModelAPICost({
  required AIProvider provider,
  required String modelName,
  required int promptTokens,
  required int completionTokens,
  int cachedTokens = 0,  // 支援快取輸入
  String currency = 'TWD',
}) {
  // 支援快取輸入的差異化定價
  if (cachedTokens > 0 && pricing.supportsCachedInput) {
    final nonCachedTokens = promptTokens - cachedTokens;
    inputCostUSD = (nonCachedTokens / 1000.0) * pricing.inputCostPer1000 +
                  (cachedTokens / 1000.0) * pricing.cachedInputCostPer1000!;
  }
  // ...
}
```

### UI 組件實作

#### 費用顯示組件
```dart
Widget _buildAPICostDisplay() {
  return Container(
    decoration: BoxDecoration(
      color: Colors.grey[50],
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Colors.grey[300]!),
    ),
    child: Column(
      children: [
        // 費用總覽
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('API 費用'),
            Text(APICostCalculatorService.formatCost(_apiCost!)),
          ],
        ),
        // 詳細資訊（可展開）
        if (_showCostDetails) ...[
          _buildCostDetailRow('輸入 Tokens', '${_promptTokens}'),
          _buildCostDetailRow('輸出 Tokens', '${_completionTokens}'),
          _buildProviderInfo(),
          _buildCostSavingTip(),
        ],
      ],
    ),
  );
}
```

#### 費用計算整合
```dart
/// 在解讀完成時自動計算費用
onComplete: (content) {
  setState(() {
    _isStreamingComplete = true;
    _streamingContent = content;
  });
  // 保存解讀紀錄
  if (content.isNotEmpty) {
    _saveInterpretationRecord(content);
  }
  // 計算 API 費用
  _calculateAndDisplayAPICost(content);
},
```

### 狀態管理

#### 新增費用相關狀態
```dart
class _AIInterpretationResultPageState extends State<AIInterpretationResultPage> {
  // API 費用相關狀態
  double? _apiCost;              // API 費用
  int? _promptTokens;            // 輸入 tokens
  int? _completionTokens;        // 輸出 tokens
  String? _costCurrency = 'TWD'; // 費用貨幣
  bool _showCostDetails = false; // 是否顯示費用詳情
}
```

#### 貨幣切換功能
```dart
/// 切換費用顯示貨幣
void _toggleCostCurrency() {
  setState(() {
    _costCurrency = _costCurrency == 'TWD' ? 'USD' : 'TWD';
  });
  
  // 重新計算費用
  if (_promptTokens != null && _completionTokens != null) {
    _recalculateAPICost();
  }
}
```

## 📱 使用者體驗

### 1. 直觀的費用顯示
- **簡潔介面**：費用資訊以卡片形式顯示在解讀內容下方
- **可展開詳情**：點擊展開按鈕查看詳細的費用分解
- **即時更新**：解讀完成後立即顯示費用資訊

### 2. 智能提示系統
- **節省建議**：自動比較所有可用模型，提供最經濟的選擇建議
- **百分比顯示**：清楚顯示可節省的金額和百分比
- **模型資訊**：顯示當前使用的模型名稱和定價資訊

### 3. 多貨幣支援
- **一鍵切換**：在 AppBar 選單中提供貨幣切換選項
- **智能格式化**：根據貨幣類型自動調整顯示精度
- **即時轉換**：切換貨幣時立即重新計算和顯示

## 🔧 技術特色

### 1. 精確的 Token 估算
```dart
/// 基於文本內容的 Token 估算
static int estimateTokens(String text) {
  final chineseCharCount = text.runes.where((rune) {
    return rune >= 0x4E00 && rune <= 0x9FFF; // 中文字符範圍
  }).length;
  
  final otherCharCount = text.length - chineseCharCount;
  
  // 中文字符：1.5 字符 ≈ 1 token
  // 其他字符：4 字符 ≈ 1 token
  final estimatedTokens = (chineseCharCount / 1.5 + otherCharCount / 4).ceil();
  
  return estimatedTokens;
}
```

### 2. 智能費用格式化
```dart
/// 根據金額大小和貨幣類型智能格式化
static String formatCost(double cost, {String currency = 'TWD'}) {
  if (cost == 0.0) {
    return currency.toUpperCase() == 'USD' ? 'Free' : '免費';
  }

  if (currency.toUpperCase() == 'USD') {
    // 美元：小額顯示千分之幾，一般顯示到小數點後4位
    if (cost < 0.001) {
      return '\$${(cost * 1000).toStringAsFixed(2)}‰';
    } else {
      return '\$${cost.toStringAsFixed(4)}';
    }
  } else {
    // 台幣：根據金額大小調整精度
    if (cost < 0.01) {
      return 'NT\$${cost.toStringAsFixed(4)}';
    } else {
      return 'NT\$${cost.toStringAsFixed(2)}';
    }
  }
}
```

### 3. 模型比較和建議
```dart
/// 獲取費用節省建議
static String getCostSavingTip(
  AIProvider provider,
  int promptTokens,
  int completionTokens, {
  String? currentModelName,
}) {
  // 比較所有提供商和 OpenAI 各模型的費用
  // 找出最經濟的選擇並計算節省金額和百分比
  if (bestOption != null && lowestCost < currentCost) {
    final savings = currentCost - lowestCost;
    final savingsPercent = ((savings / currentCost) * 100).toInt();
    return '💡 使用 $bestOption 可節省 ${formatCost(savings)} ($savingsPercent%)';
  }
}
```

## 🚀 效能優化

### 1. 非同步計算
- **背景計算**：費用計算在背景進行，不阻塞 UI
- **快取機制**：計算結果快取，避免重複計算
- **延遲載入**：只在解讀完成後才進行費用計算

### 2. 記憶體管理
- **狀態清理**：適當清理不需要的狀態變數
- **資源釋放**：及時釋放計算過程中的臨時資源

## 📊 成本效益分析

### 模型費用排序（1000 輸入 + 1000 輸出 tokens）
1. **Groq**: 免費 🆓
2. **GPT-5-nano**: $0.245 USD (約 NT$7.7)
3. **GPT-4o-mini**: $0.375 USD (約 NT$11.8)
4. **GPT-4.1-nano**: $0.25 USD (約 NT$7.9)
5. **GPT-4.1-mini**: $1.00 USD (約 NT$31.5)
6. **GPT-5-mini**: $1.125 USD (約 NT$35.4)
7. **GPT-4.1**: $5.00 USD (約 NT$157.5)
8. **GPT-4o**: $6.25 USD (約 NT$196.9)
9. **GPT-5**: $5.625 USD (約 NT$177.2)
10. **GPT-4o-2024-05-13**: $10.00 USD (約 NT$315.0)

### 使用建議
- **開發測試**: 使用 Groq（免費）或 GPT-4o-mini（低成本）
- **一般應用**: 使用 GPT-5-nano 或 GPT-4.1-nano（平衡性價比）
- **高品質需求**: 使用 GPT-5 或 GPT-4o（最佳效果）
- **特殊需求**: 使用 GPT-4o-2024-05-13（最高品質）

## 🔮 未來擴展

### 1. 進階功能
- **費用預算管理**：設定每日/月度費用預算
- **使用量趨勢**：顯示歷史使用量和費用趨勢
- **自動模型選擇**：根據預算自動選擇最適合的模型

### 2. 整合功能
- **費用報告**：生成詳細的費用使用報告
- **成本優化**：基於使用模式提供成本優化建議
- **預測分析**：預測未來的費用支出

## 📈 監控指標

### 費用追蹤
- 每次解讀的實際費用
- 不同模型的使用分布
- 費用節省建議的採用率

### 使用者行為
- 費用詳情的查看率
- 貨幣切換的使用頻率
- 模型選擇的變化趨勢

這個實作為用戶提供了透明、準確的 API 費用資訊，幫助他們做出更明智的 AI 模型選擇，同時為應用提供了重要的成本控制和優化功能。
