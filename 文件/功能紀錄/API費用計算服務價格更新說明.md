# API 費用計算服務價格更新說明

## 📋 更新概述

更新了 `APICostCalculatorService` 中的 AI 模型定價資訊，新增了最新的 OpenAI 模型價格表，並擴展了服務功能以支援更精確的費用計算。

## 🎯 主要更新內容

### 1. 新增 OpenAI 模型定價表

根據 2025-01-09 的最新價格資訊，新增了完整的 OpenAI 模型定價表：

#### GPT-5 系列
- **GPT-5**: 輸入 $0.625/1K, 輸出 $5.00/1K, 快取輸入 $0.0625/1K
- **GPT-5-mini**: 輸入 $0.125/1K, 輸出 $1.00/1K, 快取輸入 $0.0125/1K  
- **GPT-5-nano**: 輸入 $0.025/1K, 輸出 $0.20/1K, 快取輸入 $0.0025/1K

#### GPT-4.1 系列
- **GPT-4.1**: 輸入 $1.00/1K, 輸出 $4.00/1K
- **GPT-4.1-mini**: 輸入 $0.20/1K, 輸出 $0.80/1K
- **GPT-4.1-nano**: 輸入 $0.05/1K, 輸出 $0.20/1K

#### GPT-4o 系列
- **GPT-4o**: 輸入 $1.25/1K, 輸出 $5.00/1K
- **GPT-4o-2024-05-13**: 輸入 $2.50/1K, 輸出 $7.50/1K
- **GPT-4o-mini**: 輸入 $0.075/1K, 輸出 $0.30/1K

### 2. 擴展 APIPricing 類別

新增了對快取輸入定價的支援：

```dart
class APIPricing {
  final double inputCostPer1000;         // 每 1000 輸入 tokens 的費用
  final double outputCostPer1000;        // 每 1000 輸出 tokens 的費用
  final String modelName;                // 模型名稱
  final double? cachedInputCostPer1000;  // 每 1000 快取輸入 tokens 的費用（可選）

  /// 是否支援快取輸入定價
  bool get supportsCachedInput => cachedInputCostPer1000 != null;
}
```

### 3. 新增模型特定費用計算

新增了 `calculateModelAPICost` 方法，支援：
- 指定具體的模型名稱進行費用計算
- 快取輸入 tokens 的差異化定價
- 更精確的費用估算

```dart
static double calculateModelAPICost({
  required AIProvider provider,
  required String modelName,
  required int promptTokens,
  required int completionTokens,
  int cachedTokens = 0,
  String currency = 'TWD',
})
```

### 4. 增強費用節省建議

更新了 `getCostSavingTip` 方法：
- 支援比較不同 OpenAI 模型的費用
- 顯示節省金額和百分比
- 提供更精確的建議

### 5. 新增模型費用比較功能

新增了 `getModelCostComparison` 方法：
- 比較所有可用模型的費用
- 支援不同貨幣顯示
- 幫助用戶選擇最經濟的模型

## 🔧 技術實作細節

### 資料結構更新

#### 新增 OpenAI 模型定價映射
```dart
static const Map<String, APIPricing> _openaiModelPricing = {
  'gpt-5': APIPricing(
    inputCostPer1000: 0.625,
    outputCostPer1000: 5.00,
    modelName: 'GPT-5',
    cachedInputCostPer1000: 0.0625,
  ),
  // ... 其他模型
};
```

#### 更新提供商預設定價
```dart
static const Map<AIProvider, APIPricing> _pricingModels = {
  AIProvider.openai: APIPricing(
    inputCostPer1000: 1.25,    // 更新為 GPT-4o 價格
    outputCostPer1000: 5.00,
    modelName: 'GPT-4o',
  ),
  // ... 其他提供商保持不變
};
```

### 方法增強

#### 智能定價選擇
```dart
static APIPricing? getPricing(AIProvider provider, {String? modelName}) {
  // 如果是 OpenAI 且指定了模型名稱，優先使用具體模型的定價
  if (provider == AIProvider.openai && modelName != null) {
    final openaiPricing = _openaiModelPricing[modelName.toLowerCase()];
    if (openaiPricing != null) {
      return openaiPricing;
    }
  }
  
  // 使用預設定價
  return _pricingModels[provider];
}
```

#### 快取輸入費用計算
```dart
// 計算輸入費用（區分快取和非快取）
double inputCostUSD = 0.0;
if (cachedTokens > 0 && pricing.supportsCachedInput) {
  // 有快取輸入且支援快取定價
  final nonCachedTokens = promptTokens - cachedTokens;
  inputCostUSD = (nonCachedTokens / 1000.0) * pricing.inputCostPer1000 +
                (cachedTokens / 1000.0) * pricing.cachedInputCostPer1000!;
} else {
  // 沒有快取或不支援快取定價
  inputCostUSD = (promptTokens / 1000.0) * pricing.inputCostPer1000;
}
```

## 📱 使用者體驗改善

### 1. 更精確的費用顯示
- 根據實際使用的模型顯示準確費用
- 支援快取輸入的差異化定價
- 提供詳細的費用分解

### 2. 智能節省建議
- 比較所有可用模型的費用
- 顯示具體的節省金額和百分比
- 幫助用戶做出經濟的選擇

### 3. 模型費用透明化
- 顯示當前使用的模型名稱
- 提供模型的詳細定價資訊
- 支援費用比較功能

## 🔮 向後相容性

### 保持現有 API 相容
- 原有的 `calculateAPICost` 方法保持不變
- 新增的功能為可選參數
- 不影響現有的費用計算邏輯

### 漸進式升級
- 可以逐步採用新的模型特定計算
- 支援混合使用舊有和新的計算方法
- 提供平滑的遷移路徑

## 🚀 未來擴展

### 1. 動態定價更新
- 支援從 Remote Config 載入最新價格
- 自動更新定價資訊
- 價格變動通知

### 2. 更多提供商支援
- 新增其他 AI 提供商的詳細模型定價
- 支援更多模型的快取定價
- 擴展比較功能

### 3. 費用預測
- 基於使用模式預測月度費用
- 提供費用預算管理
- 使用量趨勢分析

## 📊 價格對比分析

### 成本效益排序（以 1000 輸入 + 1000 輸出 tokens 為例）

1. **Groq**: 免費
2. **GPT-5-nano**: $0.245 USD
3. **GPT-4o-mini**: $0.375 USD
4. **GPT-4.1-nano**: $0.25 USD
5. **GPT-4.1-mini**: $1.00 USD
6. **GPT-5-mini**: $1.125 USD
7. **GPT-4.1**: $5.00 USD
8. **GPT-4o**: $6.25 USD
9. **GPT-5**: $5.625 USD
10. **GPT-4o-2024-05-13**: $10.00 USD

### 建議使用策略

- **開發測試**: 使用 Groq 或 GPT-4o-mini
- **一般應用**: 使用 GPT-5-nano 或 GPT-4.1-nano
- **高品質需求**: 使用 GPT-5 或 GPT-4o
- **特殊需求**: 使用 GPT-4o-2024-05-13

## 🧪 測試建議

### 功能測試
1. **價格計算準確性**: 驗證各模型的費用計算
2. **快取定價**: 測試快取輸入的費用計算
3. **貨幣轉換**: 驗證 USD/TWD 轉換的準確性
4. **節省建議**: 測試費用節省建議的正確性

### 效能測試
1. **計算速度**: 測試費用計算的響應時間
2. **記憶體使用**: 監控定價資料的記憶體佔用
3. **快取效率**: 驗證定價資料的快取機制

## 📈 監控指標

### 費用追蹤
- 每日/月度 API 費用統計
- 不同模型的使用分布
- 費用節省效果追蹤

### 使用模式
- 模型選擇偏好分析
- 費用敏感度分析
- 節省建議採用率

這次更新大幅提升了 API 費用計算的準確性和實用性，為用戶提供了更好的費用控制和優化建議。
