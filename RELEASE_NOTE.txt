Release Note:
- feat: 增強 AI 費用計算與模型感知能力
- feat: 新增 API 費用計算與顯示功能
- fix: 確保 AstroEvent 中圖標正確加載
- feat: 新增 GPT-5 模型支援並調整 OpenAI 請求體
- feat: 新增事件偵測設定服務及相關 UI
- feat: 新增分類事件時間軸功能
- feat: 細化事件類型判斷邏輯，整合行星所在宮位資訊
- refactor: 優化推運分析邏輯，直接使用星盤中的相位數據
- feat: 優化事件偵測服務及時間線數據處理
- feat: 新增事件偵測快取有效期設定功能
- feat: 新增事件偵測快取有效期設定功能
- docs: 新增事件偵測功能
- feat: 調整建置號碼格式並優化其顯示
- chore: 更新 macOS Info.plist 資訊
- feat: 新增統一建置腳本 `build_all.sh` 及說明文件
- fix: 將應用程式名稱從 Astreal 修正為 AstReal
- feat: 重構 ChartTypeSettings 以支持 Data Class 並保持向後相容性
- feat: 每日星象行星位置計算調整及UI優化
- fix: 調整比較盤和行運盤相位資訊中行星歸屬描述順序
- feat: 新增「原始內容」頁面，顯示AI解讀的輸入信息
- feat: 調整 OpenAI GPT 模型參數
- feat: 新增出生資料後自動導航至星盤頁面
- chore: 為腳本新增 Flutter PATH 環境變數
- fix: 移除性別選項中的「中性」並調整預設值
- feat: 優化出生資料表單 UI 與響應式佈局
- fix: 將「白羊座」統一修正為「牡羊座」
- feat: 新增占星學核心資料模型
- feat: 增強星座定義，引入類型安全的 `ZodiacSign` 模型並提供示例
- refactor: 星座符號統一由 `ZodiacDefinitions` 提供
- feat: 每日占星新增行星位置顯示
